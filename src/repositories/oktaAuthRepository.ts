/**
 * Okta Authentication Repository
 * 
 * Data access layer for Okta authentication operations.
 * Handles secure storage of tokens, user profiles, and authentication state.
 */

import { SecureStorageRepository } from './secureStorageRepository';
import type { AuthData } from './secureStorageRepository';
import type { 
	OktaAuthResult, 
	OktaUserProfile, 
	OktaTokenResponse,
	AuthenticationState 
} from '@/types/okta';

/**
 * Storage keys for Okta authentication data
 */
export const OktaStorageKeys = {
	AUTH_DATA: 'okta_auth_data',
	USER_PROFILE: 'okta_user_profile',
	AUTH_STATE: 'okta_auth_state',
	PKCE_VERIFIER: 'okta_pkce_verifier',
	OAUTH_STATE: 'okta_oauth_state',
} as const;

export type OktaStorageKey = (typeof OktaStorageKeys)[keyof typeof OktaStorageKeys];

/**
 * Repository for Okta authentication data access operations
 */
export class OktaAuthRepository {
	private secureStorage: SecureStorageRepository;

	constructor(secureStorage: SecureStorageRepository) {
		this.secureStorage = secureStorage;
	}

	/**
	 * Store complete authentication data
	 */
	async saveAuthData(authResult: OktaAuthResult): Promise<void> {
		if (!authResult.success || !authResult.accessToken) {
			throw new Error('Cannot save invalid authentication data');
		}

		const authData: AuthData = {
			token: authResult.accessToken,
			refreshToken: authResult.refreshToken || '',
			idToken: authResult.idToken,
			expiresAt: authResult.expiresAt || 0,
			tokenType: authResult.tokenType || 'Bearer',
			scope: authResult.scope,
			userProfile: authResult.userProfile ? {
				sub: authResult.userProfile.sub,
				email: authResult.userProfile.email,
				name: authResult.userProfile.name,
				preferred_username: authResult.userProfile.preferredUsername,
				given_name: authResult.userProfile.givenName,
				family_name: authResult.userProfile.familyName,
			} : undefined
		};

		try {
			await this.secureStorage.setObject(OktaStorageKeys.AUTH_DATA, authData);
			
			// Store user profile separately for easier access
			if (authResult.userProfile) {
				await this.secureStorage.setObject(OktaStorageKeys.USER_PROFILE, authResult.userProfile);
			}
		} catch (error) {
			throw new Error(`Failed to save authentication data: ${error}`);
		}
	}

	/**
	 * Retrieve authentication data
	 */
	async getAuthData(): Promise<AuthData | null> {
		try {
			return await this.secureStorage.getObject<AuthData>(OktaStorageKeys.AUTH_DATA);
		} catch (error) {
			console.error('Failed to retrieve authentication data:', error);
			return null;
		}
	}

	/**
	 * Update tokens from refresh operation
	 */
	async updateTokens(tokenResponse: OktaTokenResponse): Promise<void> {
		try {
			const existingAuthData = await this.getAuthData();
			if (!existingAuthData) {
				throw new Error('No existing authentication data to update');
			}

			const updatedAuthData: AuthData = {
				...existingAuthData,
				token: tokenResponse.accessToken,
				refreshToken: tokenResponse.refreshToken || existingAuthData.refreshToken,
				idToken: tokenResponse.idToken || existingAuthData.idToken,
				expiresAt: Date.now() + (tokenResponse.expiresIn * 1000),
				tokenType: tokenResponse.tokenType,
				scope: tokenResponse.scope || existingAuthData.scope,
			};

			await this.secureStorage.setObject(OktaStorageKeys.AUTH_DATA, updatedAuthData);
		} catch (error) {
			throw new Error(`Failed to update tokens: ${error}`);
		}
	}

	/**
	 * Retrieve user profile
	 */
	async getUserProfile(): Promise<OktaUserProfile | null> {
		try {
			return await this.secureStorage.getObject<OktaUserProfile>(OktaStorageKeys.USER_PROFILE);
		} catch (error) {
			console.error('Failed to retrieve user profile:', error);
			return null;
		}
	}

	/**
	 * Store user profile
	 */
	async saveUserProfile(profile: OktaUserProfile): Promise<void> {
		try {
			await this.secureStorage.setObject(OktaStorageKeys.USER_PROFILE, profile);
		} catch (error) {
			throw new Error(`Failed to save user profile: ${error}`);
		}
	}

	/**
	 * Store authentication state
	 */
	async saveAuthState(state: AuthenticationState): Promise<void> {
		try {
			await this.secureStorage.setItem(OktaStorageKeys.AUTH_STATE, state);
		} catch (error) {
			throw new Error(`Failed to save authentication state: ${error}`);
		}
	}

	/**
	 * Retrieve authentication state
	 */
	async getAuthState(): Promise<AuthenticationState | null> {
		try {
			const state = await this.secureStorage.getItem(OktaStorageKeys.AUTH_STATE);
			return state as AuthenticationState;
		} catch (error) {
			console.error('Failed to retrieve authentication state:', error);
			return null;
		}
	}

	/**
	 * Store PKCE code verifier for OAuth flow
	 */
	async savePKCEVerifier(verifier: string): Promise<void> {
		try {
			await this.secureStorage.setItem(OktaStorageKeys.PKCE_VERIFIER, verifier);
		} catch (error) {
			throw new Error(`Failed to save PKCE verifier: ${error}`);
		}
	}

	/**
	 * Retrieve and remove PKCE code verifier
	 */
	async getPKCEVerifier(): Promise<string | null> {
		try {
			const verifier = await this.secureStorage.getItem(OktaStorageKeys.PKCE_VERIFIER);
			if (verifier) {
				// Remove after retrieval for security
				await this.secureStorage.removeItem(OktaStorageKeys.PKCE_VERIFIER);
			}
			return verifier;
		} catch (error) {
			console.error('Failed to retrieve PKCE verifier:', error);
			return null;
		}
	}

	/**
	 * Store OAuth state parameter
	 */
	async saveOAuthState(state: string): Promise<void> {
		try {
			await this.secureStorage.setItem(OktaStorageKeys.OAUTH_STATE, state);
		} catch (error) {
			throw new Error(`Failed to save OAuth state: ${error}`);
		}
	}

	/**
	 * Retrieve and remove OAuth state parameter
	 */
	async getOAuthState(): Promise<string | null> {
		try {
			const state = await this.secureStorage.getItem(OktaStorageKeys.OAUTH_STATE);
			if (state) {
				// Remove after retrieval for security
				await this.secureStorage.removeItem(OktaStorageKeys.OAUTH_STATE);
			}
			return state;
		} catch (error) {
			console.error('Failed to retrieve OAuth state:', error);
			return null;
		}
	}

	/**
	 * Check if user is authenticated based on stored data
	 */
	async isAuthenticated(): Promise<boolean> {
		try {
			const authData = await this.getAuthData();
			if (!authData || !authData.token) {
				return false;
			}

			// Check if token is expired
			const now = Date.now();
			return authData.expiresAt > now;
		} catch (error) {
			console.error('Failed to check authentication status:', error);
			return false;
		}
	}

	/**
	 * Check if token needs refresh (expires within 5 minutes)
	 */
	async needsTokenRefresh(): Promise<boolean> {
		try {
			const authData = await this.getAuthData();
			if (!authData || !authData.token) {
				return false;
			}

			const fiveMinutesFromNow = Date.now() + (5 * 60 * 1000);
			return authData.expiresAt <= fiveMinutesFromNow;
		} catch (error) {
			console.error('Failed to check if token needs refresh:', error);
			return false;
		}
	}

	/**
	 * Clear all authentication data
	 */
	async clearAuthData(): Promise<void> {
		try {
			await Promise.all([
				this.secureStorage.removeItem(OktaStorageKeys.AUTH_DATA),
				this.secureStorage.removeItem(OktaStorageKeys.USER_PROFILE),
				this.secureStorage.removeItem(OktaStorageKeys.AUTH_STATE),
				this.secureStorage.removeItem(OktaStorageKeys.PKCE_VERIFIER),
				this.secureStorage.removeItem(OktaStorageKeys.OAUTH_STATE),
			]);
		} catch (error) {
			throw new Error(`Failed to clear authentication data: ${error}`);
		}
	}

	/**
	 * Get access token for API calls
	 */
	async getAccessToken(): Promise<string | null> {
		try {
			const authData = await this.getAuthData();
			return authData?.token || null;
		} catch (error) {
			console.error('Failed to get access token:', error);
			return null;
		}
	}

	/**
	 * Get refresh token
	 */
	async getRefreshToken(): Promise<string | null> {
		try {
			const authData = await this.getAuthData();
			return authData?.refreshToken || null;
		} catch (error) {
			console.error('Failed to get refresh token:', error);
			return null;
		}
	}
}

// Export singleton instance
export const oktaAuthRepository = new OktaAuthRepository(
	new SecureStorageRepository()
);

/**
 * OktaAuthRepository Tests
 * 
 * Unit tests for the OktaAuthRepository class
 */

import { OktaAuthRepository } from '../oktaAuthRepository';
import { SecureStorageRepository } from '../secureStorageRepository';
import type { OktaAuthResult, OktaUserProfile, OktaTokenResponse } from '@/types/okta';

// Mock the SecureStorageRepository
const mockSecureStorage = {
	setObject: jest.fn(),
	getObject: jest.fn(),
	setItem: jest.fn(),
	getItem: jest.fn(),
	removeItem: jest.fn(),
} as jest.Mocked<Partial<SecureStorageRepository>>;

describe('OktaAuthRepository', () => {
	let repository: OktaAuthRepository;

	beforeEach(() => {
		jest.clearAllMocks();
		repository = new OktaAuthRepository(mockSecureStorage as SecureStorageRepository);
	});

	describe('saveAuthData', () => {
		const mockAuthResult: OktaAuthResult = {
			success: true,
			accessToken: 'access-token',
			refreshToken: 'refresh-token',
			idToken: 'id-token',
			tokenType: 'Bearer',
			expiresAt: Date.now() + 3600000,
			scope: 'openid profile email',
			userProfile: {
				sub: 'user-123',
				email: '<EMAIL>',
				name: 'Test User'
			}
		};

		it('should save authentication data successfully', async () => {
			await repository.saveAuthData(mockAuthResult);

			expect(mockSecureStorage.setObject).toHaveBeenCalledWith(
				'okta_auth_data',
				expect.objectContaining({
					token: 'access-token',
					refreshToken: 'refresh-token',
					idToken: 'id-token',
					tokenType: 'Bearer',
					scope: 'openid profile email'
				})
			);

			expect(mockSecureStorage.setObject).toHaveBeenCalledWith(
				'okta_user_profile',
				mockAuthResult.userProfile
			);
		});

		it('should throw error for invalid auth data', async () => {
			const invalidAuthResult = { ...mockAuthResult, success: false };

			await expect(repository.saveAuthData(invalidAuthResult)).rejects.toThrow(
				'Cannot save invalid authentication data'
			);
		});

		it('should throw error for missing access token', async () => {
			const invalidAuthResult = { ...mockAuthResult, accessToken: undefined };

			await expect(repository.saveAuthData(invalidAuthResult)).rejects.toThrow(
				'Cannot save invalid authentication data'
			);
		});

		it('should handle storage errors', async () => {
			mockSecureStorage.setObject?.mockRejectedValue(new Error('Storage error'));

			await expect(repository.saveAuthData(mockAuthResult)).rejects.toThrow(
				'Failed to save authentication data'
			);
		});
	});

	describe('getAuthData', () => {
		it('should retrieve authentication data', async () => {
			const mockAuthData = {
				token: 'access-token',
				refreshToken: 'refresh-token',
				expiresAt: Date.now() + 3600000
			};

			mockSecureStorage.getObject?.mockResolvedValue(mockAuthData);

			const result = await repository.getAuthData();

			expect(result).toEqual(mockAuthData);
			expect(mockSecureStorage.getObject).toHaveBeenCalledWith('okta_auth_data');
		});

		it('should handle storage errors', async () => {
			mockSecureStorage.getObject?.mockRejectedValue(new Error('Storage error'));

			const result = await repository.getAuthData();

			expect(result).toBeNull();
		});
	});

	describe('updateTokens', () => {
		const mockTokenResponse: OktaTokenResponse = {
			accessToken: 'new-access-token',
			tokenType: 'Bearer',
			expiresIn: 3600,
			refreshToken: 'new-refresh-token'
		};

		it('should update tokens successfully', async () => {
			const existingAuthData = {
				token: 'old-token',
				refreshToken: 'old-refresh-token',
				expiresAt: Date.now() + 1000
			};

			mockSecureStorage.getObject?.mockResolvedValue(existingAuthData);

			await repository.updateTokens(mockTokenResponse);

			expect(mockSecureStorage.setObject).toHaveBeenCalledWith(
				'okta_auth_data',
				expect.objectContaining({
					token: 'new-access-token',
					refreshToken: 'new-refresh-token',
					tokenType: 'Bearer'
				})
			);
		});

		it('should throw error when no existing auth data', async () => {
			mockSecureStorage.getObject?.mockResolvedValue(null);

			await expect(repository.updateTokens(mockTokenResponse)).rejects.toThrow(
				'No existing authentication data to update'
			);
		});
	});

	describe('getUserProfile', () => {
		it('should retrieve user profile', async () => {
			const mockProfile: OktaUserProfile = {
				sub: 'user-123',
				email: '<EMAIL>',
				name: 'Test User'
			};

			mockSecureStorage.getObject?.mockResolvedValue(mockProfile);

			const result = await repository.getUserProfile();

			expect(result).toEqual(mockProfile);
			expect(mockSecureStorage.getObject).toHaveBeenCalledWith('okta_user_profile');
		});

		it('should handle storage errors', async () => {
			mockSecureStorage.getObject?.mockRejectedValue(new Error('Storage error'));

			const result = await repository.getUserProfile();

			expect(result).toBeNull();
		});
	});

	describe('saveUserProfile', () => {
		it('should save user profile', async () => {
			const mockProfile: OktaUserProfile = {
				sub: 'user-123',
				email: '<EMAIL>',
				name: 'Test User'
			};

			await repository.saveUserProfile(mockProfile);

			expect(mockSecureStorage.setObject).toHaveBeenCalledWith(
				'okta_user_profile',
				mockProfile
			);
		});

		it('should handle storage errors', async () => {
			mockSecureStorage.setObject?.mockRejectedValue(new Error('Storage error'));

			await expect(repository.saveUserProfile({} as OktaUserProfile)).rejects.toThrow(
				'Failed to save user profile'
			);
		});
	});

	describe('saveAuthState', () => {
		it('should save authentication state', async () => {
			await repository.saveAuthState('authenticated');

			expect(mockSecureStorage.setItem).toHaveBeenCalledWith(
				'okta_auth_state',
				'authenticated'
			);
		});

		it('should handle storage errors', async () => {
			mockSecureStorage.setItem?.mockRejectedValue(new Error('Storage error'));

			await expect(repository.saveAuthState('authenticated')).rejects.toThrow(
				'Failed to save authentication state'
			);
		});
	});

	describe('getAuthState', () => {
		it('should retrieve authentication state', async () => {
			mockSecureStorage.getItem?.mockResolvedValue('authenticated');

			const result = await repository.getAuthState();

			expect(result).toBe('authenticated');
			expect(mockSecureStorage.getItem).toHaveBeenCalledWith('okta_auth_state');
		});

		it('should handle storage errors', async () => {
			mockSecureStorage.getItem?.mockRejectedValue(new Error('Storage error'));

			const result = await repository.getAuthState();

			expect(result).toBeNull();
		});
	});

	describe('PKCE operations', () => {
		it('should save and retrieve PKCE verifier', async () => {
			const verifier = 'test-verifier';

			await repository.savePKCEVerifier(verifier);
			expect(mockSecureStorage.setItem).toHaveBeenCalledWith('okta_pkce_verifier', verifier);

			mockSecureStorage.getItem?.mockResolvedValue(verifier);
			const result = await repository.getPKCEVerifier();

			expect(result).toBe(verifier);
			expect(mockSecureStorage.removeItem).toHaveBeenCalledWith('okta_pkce_verifier');
		});

		it('should handle PKCE storage errors', async () => {
			mockSecureStorage.setItem?.mockRejectedValue(new Error('Storage error'));

			await expect(repository.savePKCEVerifier('verifier')).rejects.toThrow(
				'Failed to save PKCE verifier'
			);
		});
	});

	describe('OAuth state operations', () => {
		it('should save and retrieve OAuth state', async () => {
			const state = 'test-state';

			await repository.saveOAuthState(state);
			expect(mockSecureStorage.setItem).toHaveBeenCalledWith('okta_oauth_state', state);

			mockSecureStorage.getItem?.mockResolvedValue(state);
			const result = await repository.getOAuthState();

			expect(result).toBe(state);
			expect(mockSecureStorage.removeItem).toHaveBeenCalledWith('okta_oauth_state');
		});
	});

	describe('isAuthenticated', () => {
		it('should return true for valid token', async () => {
			const authData = {
				token: 'access-token',
				expiresAt: Date.now() + 3600000
			};

			mockSecureStorage.getObject?.mockResolvedValue(authData);

			const result = await repository.isAuthenticated();

			expect(result).toBe(true);
		});

		it('should return false for expired token', async () => {
			const authData = {
				token: 'access-token',
				expiresAt: Date.now() - 1000
			};

			mockSecureStorage.getObject?.mockResolvedValue(authData);

			const result = await repository.isAuthenticated();

			expect(result).toBe(false);
		});

		it('should return false for missing token', async () => {
			mockSecureStorage.getObject?.mockResolvedValue(null);

			const result = await repository.isAuthenticated();

			expect(result).toBe(false);
		});
	});

	describe('needsTokenRefresh', () => {
		it('should return true for token expiring soon', async () => {
			const authData = {
				token: 'access-token',
				expiresAt: Date.now() + (2 * 60 * 1000) // 2 minutes from now
			};

			mockSecureStorage.getObject?.mockResolvedValue(authData);

			const result = await repository.needsTokenRefresh();

			expect(result).toBe(true);
		});

		it('should return false for token with plenty of time', async () => {
			const authData = {
				token: 'access-token',
				expiresAt: Date.now() + (10 * 60 * 1000) // 10 minutes from now
			};

			mockSecureStorage.getObject?.mockResolvedValue(authData);

			const result = await repository.needsTokenRefresh();

			expect(result).toBe(false);
		});
	});

	describe('clearAuthData', () => {
		it('should clear all authentication data', async () => {
			await repository.clearAuthData();

			expect(mockSecureStorage.removeItem).toHaveBeenCalledTimes(5);
			expect(mockSecureStorage.removeItem).toHaveBeenCalledWith('okta_auth_data');
			expect(mockSecureStorage.removeItem).toHaveBeenCalledWith('okta_user_profile');
			expect(mockSecureStorage.removeItem).toHaveBeenCalledWith('okta_auth_state');
			expect(mockSecureStorage.removeItem).toHaveBeenCalledWith('okta_pkce_verifier');
			expect(mockSecureStorage.removeItem).toHaveBeenCalledWith('okta_oauth_state');
		});

		it('should handle clear errors', async () => {
			mockSecureStorage.removeItem?.mockRejectedValue(new Error('Storage error'));

			await expect(repository.clearAuthData()).rejects.toThrow(
				'Failed to clear authentication data'
			);
		});
	});

	describe('token getters', () => {
		it('should get access token', async () => {
			const authData = { token: 'access-token' };
			mockSecureStorage.getObject?.mockResolvedValue(authData);

			const result = await repository.getAccessToken();

			expect(result).toBe('access-token');
		});

		it('should get refresh token', async () => {
			const authData = { refreshToken: 'refresh-token' };
			mockSecureStorage.getObject?.mockResolvedValue(authData);

			const result = await repository.getRefreshToken();

			expect(result).toBe('refresh-token');
		});
	});
});

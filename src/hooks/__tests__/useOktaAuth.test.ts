/**
 * useOktaAuth Hook Tests
 * 
 * Unit tests for the useOktaAuth React hook
 */

import { renderHook, act } from '@testing-library/react-hooks';
import { useOktaAuth } from '../useOktaAuth';
import { oktaAuthService } from '@/services/oktaAuthService';
import { OktaAuthenticationError, OktaAuthError } from '@/types/okta';

// Mock the OktaAuthService
jest.mock('@/services/oktaAuthService', () => ({
	oktaAuthService: {
		initializeAuth: jest.fn(),
		processAuthCallback: jest.fn(),
		isAuthenticated: jest.fn(),
		getUserProfile: jest.fn(),
		getAccessToken: jest.fn(),
		getAuthState: jest.fn(),
		refreshTokenIfNeeded: jest.fn(),
		logout: jest.fn(),
	}
}));

const mockOktaAuthService = oktaAuthService as jest.Mocked<typeof oktaAuthService>;

describe('useOktaAuth', () => {
	beforeEach(() => {
		jest.clearAllMocks();
		
		// Default mock implementations
		mockOktaAuthService.isAuthenticated.mockResolvedValue(false);
		mockOktaAuthService.getUserProfile.mockResolvedValue(null);
		mockOktaAuthService.getAccessToken.mockResolvedValue(null);
		mockOktaAuthService.getAuthState.mockResolvedValue('unauthenticated');
	});

	describe('initial state', () => {
		it('should initialize with default state', async () => {
			const { result, waitForNextUpdate } = renderHook(() => useOktaAuth());

			// Wait for initial load
			await waitForNextUpdate();

			expect(result.current.authState).toBe('unauthenticated');
			expect(result.current.isLoading).toBe(false);
			expect(result.current.isAuthenticated).toBe(false);
			expect(result.current.userProfile).toBeNull();
			expect(result.current.error).toBeNull();
			expect(result.current.accessToken).toBeNull();
		});

		it('should load authenticated state on mount', async () => {
			const mockProfile = {
				sub: 'user-123',
				email: '<EMAIL>',
				name: 'Test User'
			};

			mockOktaAuthService.isAuthenticated.mockResolvedValue(true);
			mockOktaAuthService.getUserProfile.mockResolvedValue(mockProfile);
			mockOktaAuthService.getAccessToken.mockResolvedValue('access-token');
			mockOktaAuthService.getAuthState.mockResolvedValue('authenticated');

			const { result, waitForNextUpdate } = renderHook(() => useOktaAuth());

			await waitForNextUpdate();

			expect(result.current.isAuthenticated).toBe(true);
			expect(result.current.userProfile).toEqual(mockProfile);
			expect(result.current.accessToken).toBe('access-token');
			expect(result.current.authState).toBe('authenticated');
		});
	});

	describe('login', () => {
		it('should initialize authentication successfully', async () => {
			const { result } = renderHook(() => useOktaAuth());

			await act(async () => {
				await result.current.login();
			});

			expect(mockOktaAuthService.initializeAuth).toHaveBeenCalled();
			expect(result.current.authState).toBe('authenticating');
		});

		it('should handle login initialization errors', async () => {
			mockOktaAuthService.initializeAuth.mockRejectedValue(
				new OktaAuthenticationError(OktaAuthError.NETWORK_ERROR, 'Network error')
			);

			const { result } = renderHook(() => useOktaAuth());

			await act(async () => {
				await result.current.login();
			});

			expect(result.current.error).toBe('Network error. Please check your connection and try again.');
			expect(result.current.authState).toBe('error');
		});
	});

	describe('handleAuthCallback', () => {
		it('should process successful callback', async () => {
			const mockAuthResult = {
				success: true,
				accessToken: 'access-token',
				userProfile: {
					sub: 'user-123',
					email: '<EMAIL>',
					name: 'Test User'
				}
			};

			mockOktaAuthService.processAuthCallback.mockResolvedValue(mockAuthResult);

			const { result } = renderHook(() => useOktaAuth());

			const response = {
				type: 'success' as const,
				code: 'auth-code',
				state: 'state'
			};

			await act(async () => {
				await result.current.handleAuthCallback(response);
			});

			expect(mockOktaAuthService.processAuthCallback).toHaveBeenCalledWith(response);
			expect(result.current.isAuthenticated).toBe(true);
			expect(result.current.userProfile).toEqual(mockAuthResult.userProfile);
			expect(result.current.accessToken).toBe('access-token');
			expect(result.current.authState).toBe('authenticated');
		});

		it('should handle callback errors', async () => {
			mockOktaAuthService.processAuthCallback.mockRejectedValue(
				new OktaAuthenticationError(OktaAuthError.INVALID_GRANT, 'Invalid grant')
			);

			const { result } = renderHook(() => useOktaAuth());

			const response = {
				type: 'error' as const,
				error: 'invalid_grant'
			};

			await act(async () => {
				await result.current.handleAuthCallback(response);
			});

			expect(result.current.error).toBe('Invalid authentication grant. Please try logging in again.');
			expect(result.current.authState).toBe('error');
		});
	});

	describe('logout', () => {
		it('should logout successfully', async () => {
			const { result } = renderHook(() => useOktaAuth());

			await act(async () => {
				await result.current.logout();
			});

			expect(mockOktaAuthService.logout).toHaveBeenCalled();
			expect(result.current.isAuthenticated).toBe(false);
			expect(result.current.userProfile).toBeNull();
			expect(result.current.accessToken).toBeNull();
			expect(result.current.authState).toBe('unauthenticated');
		});

		it('should handle logout errors', async () => {
			mockOktaAuthService.logout.mockRejectedValue(new Error('Logout failed'));

			const { result } = renderHook(() => useOktaAuth());

			await act(async () => {
				await result.current.logout();
			});

			expect(result.current.error).toBe('Logout failed');
			expect(result.current.authState).toBe('error');
		});
	});

	describe('refreshToken', () => {
		it('should refresh token successfully', async () => {
			mockOktaAuthService.refreshTokenIfNeeded.mockResolvedValue(true);
			mockOktaAuthService.getAccessToken.mockResolvedValue('new-access-token');
			mockOktaAuthService.getUserProfile.mockResolvedValue({
				sub: 'user-123',
				email: '<EMAIL>'
			});

			const { result } = renderHook(() => useOktaAuth());

			let refreshResult: boolean;
			await act(async () => {
				refreshResult = await result.current.refreshToken();
			});

			expect(refreshResult!).toBe(true);
			expect(result.current.isAuthenticated).toBe(true);
			expect(result.current.accessToken).toBe('new-access-token');
			expect(result.current.authState).toBe('authenticated');
		});

		it('should handle refresh failure', async () => {
			mockOktaAuthService.refreshTokenIfNeeded.mockResolvedValue(false);

			const { result } = renderHook(() => useOktaAuth());

			let refreshResult: boolean;
			await act(async () => {
				refreshResult = await result.current.refreshToken();
			});

			expect(refreshResult!).toBe(false);
			expect(result.current.isAuthenticated).toBe(false);
			expect(result.current.authState).toBe('token_expired');
		});

		it('should handle refresh errors', async () => {
			mockOktaAuthService.refreshTokenIfNeeded.mockRejectedValue(new Error('Refresh failed'));

			const { result } = renderHook(() => useOktaAuth());

			let refreshResult: boolean;
			await act(async () => {
				refreshResult = await result.current.refreshToken();
			});

			expect(refreshResult!).toBe(false);
			expect(result.current.error).toBe('Refresh failed');
			expect(result.current.authState).toBe('error');
		});
	});

	describe('clearError', () => {
		it('should clear error and reset to authenticated state', async () => {
			const { result } = renderHook(() => useOktaAuth());

			// Set up authenticated state with error
			await act(async () => {
				result.current.clearError();
			});

			expect(result.current.error).toBeNull();
		});
	});

	describe('retryAuth', () => {
		it('should retry token refresh for expired tokens', async () => {
			mockOktaAuthService.refreshTokenIfNeeded.mockResolvedValue(true);
			mockOktaAuthService.getAccessToken.mockResolvedValue('new-token');
			mockOktaAuthService.getUserProfile.mockResolvedValue(null);

			const { result } = renderHook(() => useOktaAuth());

			// Simulate token expired state
			act(() => {
				result.current.clearError();
			});

			await act(async () => {
				await result.current.retryAuth();
			});

			expect(mockOktaAuthService.refreshTokenIfNeeded).toHaveBeenCalled();
		});

		it('should logout if refresh fails during retry', async () => {
			mockOktaAuthService.refreshTokenIfNeeded.mockResolvedValue(false);

			const { result } = renderHook(() => useOktaAuth());

			await act(async () => {
				await result.current.retryAuth();
			});

			expect(mockOktaAuthService.logout).toHaveBeenCalled();
		});
	});

	describe('checkAuthStatus', () => {
		it('should reload authentication state', async () => {
			const { result } = renderHook(() => useOktaAuth());

			await act(async () => {
				await result.current.checkAuthStatus();
			});

			expect(mockOktaAuthService.isAuthenticated).toHaveBeenCalled();
			expect(mockOktaAuthService.getUserProfile).toHaveBeenCalled();
			expect(mockOktaAuthService.getAccessToken).toHaveBeenCalled();
			expect(mockOktaAuthService.getAuthState).toHaveBeenCalled();
		});
	});

	describe('error handling', () => {
		it('should map OktaAuthenticationError types correctly', async () => {
			const testCases = [
				{
					error: new OktaAuthenticationError(OktaAuthError.USER_CANCELLED, 'User cancelled'),
					expectedMessage: 'Authentication was cancelled',
					expectedState: 'unauthenticated'
				},
				{
					error: new OktaAuthenticationError(OktaAuthError.TOKEN_EXPIRED, 'Token expired'),
					expectedMessage: 'Token expired',
					expectedState: 'token_expired'
				},
				{
					error: new OktaAuthenticationError(OktaAuthError.NETWORK_ERROR, 'Network error'),
					expectedMessage: 'Network error. Please check your connection and try again.',
					expectedState: 'error'
				}
			];

			for (const testCase of testCases) {
				mockOktaAuthService.initializeAuth.mockRejectedValue(testCase.error);

				const { result } = renderHook(() => useOktaAuth());

				await act(async () => {
					await result.current.login();
				});

				expect(result.current.error).toBe(testCase.expectedMessage);
				expect(result.current.authState).toBe(testCase.expectedState);

				// Reset for next test
				jest.clearAllMocks();
			}
		});

		it('should handle generic errors', async () => {
			mockOktaAuthService.initializeAuth.mockRejectedValue(new Error('Generic error'));

			const { result } = renderHook(() => useOktaAuth());

			await act(async () => {
				await result.current.login();
			});

			expect(result.current.error).toBe('Generic error');
			expect(result.current.authState).toBe('error');
		});

		it('should handle string errors', async () => {
			mockOktaAuthService.initializeAuth.mockRejectedValue('String error');

			const { result } = renderHook(() => useOktaAuth());

			await act(async () => {
				await result.current.login();
			});

			expect(result.current.error).toBe('String error');
			expect(result.current.authState).toBe('error');
		});

		it('should handle unknown errors', async () => {
			mockOktaAuthService.initializeAuth.mockRejectedValue({ unknown: 'error' });

			const { result } = renderHook(() => useOktaAuth());

			await act(async () => {
				await result.current.login();
			});

			expect(result.current.error).toBe('An unknown error occurred');
			expect(result.current.authState).toBe('error');
		});
	});
});

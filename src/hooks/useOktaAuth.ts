/**
 * useOktaAuth Hook
 * 
 * React hook that provides a clean interface to the OktaAuthService.
 * Handles loading states, error handling, and authentication status.
 */

import { useState, useEffect, useCallback } from 'react';
import { oktaAuthService } from '@/services/oktaAuthService';
import { OktaAuthenticationError } from '@/types/okta';
import type {
	OktaAuthResponse,
	OktaAuthResult,
	OktaUserProfile,
	AuthenticationState
} from '@/types/okta';

/**
 * Authentication hook state
 */
export interface UseOktaAuthState {
	/** Current authentication state */
	authState: AuthenticationState;
	/** Whether an authentication operation is in progress */
	isLoading: boolean;
	/** Whether the user is authenticated */
	isAuthenticated: boolean;
	/** Current user profile (if authenticated) */
	userProfile: OktaUserProfile | null;
	/** Current error (if any) */
	error: string | null;
	/** Access token for API calls */
	accessToken: string | null;
}

/**
 * Authentication hook actions
 */
export interface UseOktaAuthActions {
	/** Initialize authentication flow */
	login: () => Promise<void>;
	/** Process authentication callback */
	handleAuthCallback: (response: OktaAuthResponse) => Promise<void>;
	/** Process pre-exchanged authentication tokens from expo-auth-session */
	handleAuthenticationTokens: (authentication: any) => Promise<void>;
	/** Logout user */
	logout: () => Promise<void>;
	/** Refresh access token */
	refreshToken: () => Promise<boolean>;
	/** Clear current error */
	clearError: () => void;
	/** Check authentication status */
	checkAuthStatus: () => Promise<void>;
	/** Retry authentication after error */
	retryAuth: () => Promise<void>;
}

/**
 * Complete hook return type
 */
export type UseOktaAuthReturn = UseOktaAuthState & UseOktaAuthActions;

/**
 * useOktaAuth Hook
 */
export function useOktaAuth(): UseOktaAuthReturn {
	const [state, setState] = useState<UseOktaAuthState>({
		authState: 'unauthenticated',
		isLoading: false,
		isAuthenticated: false,
		userProfile: null,
		error: null,
		accessToken: null,
	});

	/**
	 * Update state helper
	 */
	const updateState = useCallback((updates: Partial<UseOktaAuthState>) => {
		setState(prev => ({ ...prev, ...updates }));
	}, []);

	/**
	 * Handle authentication errors with detailed error mapping
	 */
	const handleError = useCallback((error: unknown, context: string) => {
		console.error(`❌ [useOktaAuth] ${context}:`, error);

		let errorMessage = 'An unknown error occurred';
		let authState: AuthenticationState = 'error';

		if (error instanceof OktaAuthenticationError) {
			errorMessage = error.message;

			// Map specific error types to appropriate auth states
			switch (error.errorType) {
				case 'TOKEN_EXPIRED':
				case 'REFRESH_TOKEN_EXPIRED':
					authState = 'token_expired';
					break;
				case 'USER_CANCELLED':
					authState = 'unauthenticated';
					errorMessage = 'Authentication was cancelled';
					break;
				case 'NETWORK_ERROR':
					errorMessage = 'Network error. Please check your connection and try again.';
					break;
				case 'INVALID_CLIENT':
					errorMessage = 'Invalid client configuration. Please contact support.';
					break;
				case 'INVALID_GRANT':
					errorMessage = 'Invalid authentication grant. Please try logging in again.';
					break;
				default:
					authState = 'error';
			}
		} else if (error instanceof Error) {
			errorMessage = error.message;
		} else if (typeof error === 'string') {
			errorMessage = error;
		}

		updateState({
			isLoading: false,
			error: errorMessage,
			authState
		});
	}, [updateState]);

	/**
	 * Load authentication state from storage
	 */
	const loadAuthState = useCallback(async () => {
		try {
			const [isAuthenticated, userProfile, accessToken, authState] = await Promise.all([
				oktaAuthService.isAuthenticated(),
				oktaAuthService.getUserProfile(),
				oktaAuthService.getAccessToken(),
				oktaAuthService.getAuthState(),
			]);

			updateState({
				isAuthenticated,
				userProfile,
				accessToken,
				authState,
				error: null,
			});
		} catch (error) {
			handleError(error, 'Failed to load authentication state');
		}
	}, [updateState, handleError]);

	/**
	 * Initialize authentication flow
	 */
	const login = useCallback(async () => {
		try {
			updateState({ isLoading: true, error: null });
			
			console.log('🔐 [useOktaAuth] Initializing authentication...');
			await oktaAuthService.initializeAuth();
			
			// Note: The actual browser redirect happens in the component
			// This just prepares the authentication request
			updateState({ 
				isLoading: false,
				authState: 'authenticating'
			});
		} catch (error) {
			handleError(error, 'Failed to initialize authentication');
		}
	}, [updateState, handleError]);

	/**
	 * Process authentication callback
	 */
	const handleAuthCallback = useCallback(async (response: OktaAuthResponse) => {
		try {
			updateState({ isLoading: true, error: null });

			console.log('🔄 [useOktaAuth] Processing authentication callback...');
			const authResult: OktaAuthResult = await oktaAuthService.processAuthCallback(response);

			if (authResult.success) {
				updateState({
					isLoading: false,
					isAuthenticated: true,
					userProfile: authResult.userProfile || null,
					accessToken: authResult.accessToken || null,
					authState: 'authenticated',
					error: null,
				});

				console.log('✅ [useOktaAuth] Authentication successful');
			} else {
				throw new Error(authResult.error || 'Authentication failed');
			}
		} catch (error) {
			handleError(error, 'Failed to process authentication callback');
		}
	}, [updateState, handleError]);

	/**
	 * Process pre-exchanged authentication tokens from expo-auth-session
	 */
	const handleAuthenticationTokens = useCallback(async (authentication: any) => {
		try {
			updateState({ isLoading: true, error: null });

			console.log('🔄 [useOktaAuth] Processing pre-exchanged authentication tokens...');
			const authResult: OktaAuthResult = await oktaAuthService.processAuthenticationTokens(authentication);

			if (authResult.success) {
				updateState({
					isLoading: false,
					isAuthenticated: true,
					userProfile: authResult.userProfile || null,
					accessToken: authResult.accessToken || null,
					authState: 'authenticated',
					error: null,
				});

				console.log('✅ [useOktaAuth] Pre-exchanged token authentication successful');
			} else {
				throw new Error(authResult.error || 'Authentication failed');
			}
		} catch (error) {
			handleError(error, 'Failed to process pre-exchanged authentication tokens');
		}
	}, [updateState, handleError]);

	/**
	 * Logout user
	 */
	const logout = useCallback(async () => {
		try {
			updateState({ isLoading: true, error: null });
			
			console.log('🚪 [useOktaAuth] Logging out...');
			await oktaAuthService.logout();
			
			updateState({
				isLoading: false,
				isAuthenticated: false,
				userProfile: null,
				accessToken: null,
				authState: 'unauthenticated',
				error: null,
			});
			
			console.log('✅ [useOktaAuth] Logout successful');
		} catch (error) {
			handleError(error, 'Failed to logout');
		}
	}, [updateState, handleError]);

	/**
	 * Refresh access token
	 */
	const refreshToken = useCallback(async (): Promise<boolean> => {
		try {
			updateState({ isLoading: true, error: null, authState: 'refreshing_token' });
			
			console.log('🔄 [useOktaAuth] Refreshing token...');
			const success = await oktaAuthService.refreshTokenIfNeeded();
			
			if (success) {
				const [accessToken, userProfile] = await Promise.all([
					oktaAuthService.getAccessToken(),
					oktaAuthService.getUserProfile(),
				]);
				
				updateState({
					isLoading: false,
					isAuthenticated: true,
					accessToken,
					userProfile,
					authState: 'authenticated',
					error: null,
				});
				
				console.log('✅ [useOktaAuth] Token refresh successful');
				return true;
			} else {
				updateState({
					isLoading: false,
					isAuthenticated: false,
					accessToken: null,
					userProfile: null,
					authState: 'token_expired',
				});
				
				console.log('❌ [useOktaAuth] Token refresh failed');
				return false;
			}
		} catch (error) {
			handleError(error, 'Failed to refresh token');
			return false;
		}
	}, [updateState, handleError]);

	/**
	 * Clear current error and reset to appropriate state
	 */
	const clearError = useCallback(() => {
		updateState({
			error: null,
			authState: state.isAuthenticated ? 'authenticated' : 'unauthenticated'
		});
	}, [updateState, state.isAuthenticated]);

	/**
	 * Retry authentication after error
	 */
	const retryAuth = useCallback(async () => {
		if (state.authState === 'token_expired') {
			// Try to refresh token first
			const refreshed = await refreshToken();
			if (!refreshed) {
				// If refresh fails, user needs to login again
				await logout();
			}
		} else {
			// For other errors, clear error state and allow user to retry
			clearError();
		}
	}, [state.authState, refreshToken, logout, clearError]);

	/**
	 * Check authentication status
	 */
	const checkAuthStatus = useCallback(async () => {
		await loadAuthState();
	}, [loadAuthState]);

	/**
	 * Load initial authentication state on mount
	 */
	useEffect(() => {
		loadAuthState();
	}, [loadAuthState]);

	/**
	 * Auto-refresh token when it's about to expire
	 */
	useEffect(() => {
		if (!state.isAuthenticated || state.authState === 'refreshing_token') {
			return;
		}

		// Check token expiration every minute
		const interval = setInterval(async () => {
			try {
				const needsRefresh = await oktaAuthService.refreshTokenIfNeeded();
				if (needsRefresh) {
					console.log('🔄 [useOktaAuth] Auto-refreshing token...');
					await refreshToken();
				}
			} catch (error) {
				console.error('❌ [useOktaAuth] Auto-refresh failed:', error);
			}
		}, 60000); // Check every minute

		return () => clearInterval(interval);
	}, [state.isAuthenticated, state.authState, refreshToken]);

	return {
		// State
		authState: state.authState,
		isLoading: state.isLoading,
		isAuthenticated: state.isAuthenticated,
		userProfile: state.userProfile,
		error: state.error,
		accessToken: state.accessToken,

		// Actions
		login,
		handleAuthCallback,
		handleAuthenticationTokens,
		logout,
		refreshToken,
		clearError,
		checkAuthStatus,
		retryAuth,
	};
}

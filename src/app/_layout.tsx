import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';
import { useColorScheme } from '@/hooks/useColorScheme';

import { Provider } from 'react-redux';
import { store } from '@/store';

export default function RootLayout() {
	const colorScheme = useColorScheme();
	const [loaded] = useFonts({
		SpaceMono: require('@assets/fonts/SpaceMono-Regular.ttf'),
	});

	if (!loaded) {
		// Async font loading only occurs in development.
		return null;
	}

	return (
		<ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
			<Provider store={store}>
				<Stack>
					<Stack.Screen name='(tabs)' options={{ headerShown: false, title: 'Home' }} />
					<Stack.Screen name='+not-found' />
					<Stack.Screen name='hello' options={{ title: 'Hello Screen' }} />
					<Stack.Screen name='callback' options={{ title: 'Authentication', headerShown: false }} />
				</Stack>
			</Provider>
			<StatusBar style='auto' />
		</ThemeProvider>
	);
}

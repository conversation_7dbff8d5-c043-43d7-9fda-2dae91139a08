/**
 * OAuth Callback Handler
 *
 * Handles OAuth redirects from Okta authentication.
 * Processes the authorization code and displays the result.
 *
 * TEAM NOTES:
 * - Web: Receives callback at /callback route
 * - Mobile: Receives callback via custom scheme learningcoachcommunity://callback
 * - In production: Exchange auth code for tokens on your backend
 */

import React, { useEffect, useState, useRef } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, Platform } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as WebBrowser from 'expo-web-browser';
import { OktaAuthenticationError } from '@/types/okta';

export default function CallbackScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();

  const [isProcessing, setIsProcessing] = useState(false);
  const [processingMessage, setProcessingMessage] = useState('Processing authentication...');
  const [hasProcessed, setHasProcessed] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);
  const processedRef = useRef(false);

  useEffect(() => {
    // Prevent multiple processing attempts
    if (processedRef.current || hasProcessed) {
      console.log('🔄 Callback already processed, skipping...');
      return;
    }

    console.log('🔄 Callback screen mounted with params:', params);
    console.log('🔄 Platform:', Platform.OS);
    console.log('🔄 Current URL:', typeof window !== 'undefined' ? window.location.href : 'N/A (native)');

    // Complete the web browser auth session to close any open popups/browsers
    console.log('🔄 Calling WebBrowser.maybeCompleteAuthSession()');
    WebBrowser.maybeCompleteAuthSession();

    // Extract authentication result from URL parameters
    const code = Array.isArray(params.code) ? params.code[0] : params.code;
    const error = Array.isArray(params.error) ? params.error[0] : params.error;
    const state = Array.isArray(params.state) ? params.state[0] : params.state;

    const processCallback = async () => {
      if (isProcessing || processedRef.current) return; // Prevent double processing

      processedRef.current = true;
      setIsProcessing(true);
      setHasProcessed(true);

      try {

        if (code) {
          console.log('✅ Authentication SUCCESS! Authorization code received');
          console.log('🔐 State parameter:', state);
          console.log('🔐 Platform:', Platform.OS);

          setProcessingMessage('Processing authentication...');

          // Following the working PoC pattern - just show success with the auth code
          // In a real app, you would send the auth code to your backend for token exchange
          console.log('✅ [Callback] Authorization code received:', (code as string).substring(0, 20) + '...');
          console.log('🎯 [Callback] Using mock authentication for demonstration');

          setProcessingMessage('Authentication successful! Redirecting...');
        } else if (error) {
          console.error('❌ Authentication ERROR:', error);

          setProcessingMessage('Authentication failed');
          setAuthError(error as string);
        } else {
          console.log('🚫 Authentication cancelled or invalid callback');

          setProcessingMessage('Authentication cancelled');
          setAuthError('Authentication was cancelled');
        }
      } catch (callbackError) {
        console.error('❌ Failed to process callback:', callbackError);
        setProcessingMessage('Failed to process authentication');

        // Handle specific error types
        if (callbackError instanceof OktaAuthenticationError) {
          const errorMsg = `Authentication failed: ${callbackError.message}`;
          setProcessingMessage(errorMsg);
          setAuthError(errorMsg);
        } else if (callbackError instanceof Error) {
          const errorMsg = `Error: ${callbackError.message}`;
          setProcessingMessage(errorMsg);
          setAuthError(errorMsg);
        } else {
          const errorMsg = 'An unknown error occurred during authentication';
          setProcessingMessage(errorMsg);
          setAuthError(errorMsg);
        }
      } finally {
        setIsProcessing(false);
      }
    };

    processCallback();

    // Platform-specific browser cleanup
    if (Platform.OS === 'ios') {
      console.log('🍎 iOS: Scheduling browser close...');
      setTimeout(() => {
        WebBrowser.dismissBrowser();
        WebBrowser.maybeCompleteAuthSession();
      }, 1000);
    } else if (Platform.OS === 'android') {
      console.log('🤖 Android: Handling post-auth cleanup...');
      setTimeout(() => {
        WebBrowser.maybeCompleteAuthSession();
      }, 300);
    }

    // Navigate back to the Okta PoC screen after processing
    const timer = setTimeout(() => {
      console.log('🔄 Navigating back to Okta PoC screen');
      router.replace('/(tabs)/okta-poc');
    }, 3000);

    return () => clearTimeout(timer);
  }, []); // Empty dependency array to run only once

  // Determine display state
  const code = Array.isArray(params.code) ? params.code[0] : params.code;
  const error = Array.isArray(params.error) ? params.error[0] : params.error;
  const isSuccess = !!code && !authError && !isProcessing;
  const isError = !!error || !!authError;
  const isProcessingAuth = isProcessing;

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {isSuccess && (
          <>
            <View style={styles.successIcon}>
              <Text style={styles.successEmoji}>✅</Text>
            </View>
            <Text style={styles.successTitle}>Authentication Successful!</Text>
            <Text style={styles.successSubtitle}>
              Tokens have been securely stored.
              {Platform.OS === 'ios' ? ' Closing browser...' : ' Redirecting back to app...'}
            </Text>
          </>
        )}

        {isError && (
          <>
            <View style={styles.errorIcon}>
              <Text style={styles.errorEmoji}>❌</Text>
            </View>
            <Text style={styles.errorTitle}>Authentication Failed</Text>
            <Text style={styles.errorSubtitle}>
              {authError || error || 'An error occurred during authentication'}
            </Text>
          </>
        )}

        {isProcessingAuth && (
          <>
            <ActivityIndicator size="large" color="#3B82F6" style={styles.spinner} />
            <Text style={styles.title}>{processingMessage}</Text>
            <Text style={styles.subtitle}>
              Please wait while we complete your login
            </Text>
          </>
        )}

        {/* Debug info - remove in production */}
        <View style={styles.debugSection}>
          <Text style={styles.debugTitle}>Debug Info:</Text>
          <Text style={styles.debugText}>
            Processing: {isProcessingAuth ? 'Yes' : 'No'}
          </Text>
          <Text style={styles.debugText}>
            Params: {JSON.stringify(params, null, 2)}
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  spinner: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 32,
  },
  debugSection: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    width: '100%',
    maxWidth: 400,
  },
  debugTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  debugText: {
    fontSize: 12,
    color: '#6B7280',
    fontFamily: 'monospace',
  },
  successIcon: {
    marginBottom: 20,
  },
  successEmoji: {
    fontSize: 64,
    textAlign: 'center',
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#10B981',
    marginBottom: 8,
    textAlign: 'center',
  },
  successSubtitle: {
    fontSize: 16,
    color: '#059669',
    textAlign: 'center',
    marginBottom: 32,
  },
  errorIcon: {
    marginBottom: 20,
  },
  errorEmoji: {
    fontSize: 64,
    textAlign: 'center',
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#EF4444',
    marginBottom: 8,
    textAlign: 'center',
  },
  errorSubtitle: {
    fontSize: 16,
    color: '#DC2626',
    textAlign: 'center',
    marginBottom: 32,
  },
});

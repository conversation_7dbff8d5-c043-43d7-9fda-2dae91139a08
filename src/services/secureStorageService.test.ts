/**
 * Tests for SecureStorageService
 */

import { SecureStorageService, AuthData, UserPreferences } from './secureStorageService';
import { secureStorageRepository } from '../repositories/secureStorageRepository';

// Mock the repository
jest.mock('../repositories/secureStorageRepository');

const mockRepository = secureStorageRepository as jest.Mocked<typeof secureStorageRepository>;

describe('SecureStorageService', () => {
	let service: SecureStorageService;

	const mockAuthData: AuthData = {
		token: 'test-token',
		refreshToken: 'test-refresh-token',
		expiresAt: Date.now() + 3600000, // 1 hour from now
	};

	const mockUserPreferences: UserPreferences = {
		theme: 'dark',
		language: 'en',
		notifications: true,
		biometricEnabled: false,
	};

	beforeEach(() => {
		jest.clearAllMocks();
		service = new SecureStorageService();
		jest.spyOn(console, 'error').mockImplementation(() => {});
	});

	afterEach(() => {
		jest.restoreAllMocks();
	});

	describe('saveAuthData', () => {
		it('should save valid authentication data', async () => {
			mockRepository.saveAuthData.mockResolvedValue();

			await service.saveAuthData(mockAuthData);

			expect(mockRepository.saveAuthData).toHaveBeenCalledWith(mockAuthData);
		});

		it('should throw error for missing token', async () => {
			const invalidAuthData = { ...mockAuthData, token: '' };

			await expect(service.saveAuthData(invalidAuthData)).rejects.toThrow(
				'Authentication data must include access token',
			);
		});

		it('should throw error for missing refresh token', async () => {
			const invalidAuthData = { ...mockAuthData, refreshToken: '' };

			await expect(service.saveAuthData(invalidAuthData)).rejects.toThrow(
				'Authentication data must include refresh token',
			);
		});

		it('should throw error for expired token', async () => {
			const expiredAuthData = { ...mockAuthData, expiresAt: Date.now() - 1000 };

			await expect(service.saveAuthData(expiredAuthData)).rejects.toThrow(
				'Authentication data must include valid expiration time',
			);
		});
	});

	describe('getAuthData', () => {
		it('should retrieve authentication data', async () => {
			mockRepository.getAuthData.mockResolvedValue(mockAuthData);

			const result = await service.getAuthData();

			expect(result).toEqual(mockAuthData);
			expect(mockRepository.getAuthData).toHaveBeenCalled();
		});

		it('should return null when repository returns null', async () => {
			mockRepository.getAuthData.mockResolvedValue(null);

			const result = await service.getAuthData();

			expect(result).toBeNull();
		});

		it('should handle errors gracefully', async () => {
			mockRepository.getAuthData.mockRejectedValue(new Error('Storage error'));

			const result = await service.getAuthData();

			expect(result).toBeNull();
			expect(console.error).toHaveBeenCalled();
		});
	});

	describe('isAuthenticated', () => {
		it('should return true for valid non-expired token', async () => {
			const validAuthData = { ...mockAuthData, expiresAt: Date.now() + 3600000 };
			mockRepository.getAuthData.mockResolvedValue(validAuthData);

			const result = await service.isAuthenticated();

			expect(result).toBe(true);
		});

		it('should return false for expired token', async () => {
			const expiredAuthData = { ...mockAuthData, expiresAt: Date.now() - 1000 };
			mockRepository.getAuthData.mockResolvedValue(expiredAuthData);

			const result = await service.isAuthenticated();

			expect(result).toBe(false);
		});

		it('should return false when no auth data exists', async () => {
			mockRepository.getAuthData.mockResolvedValue(null);

			const result = await service.isAuthenticated();

			expect(result).toBe(false);
		});
	});

	describe('refreshAuthIfNeeded', () => {
		it('should return true for token that does not need refresh', async () => {
			const futureAuthData = { ...mockAuthData, expiresAt: Date.now() + 3600000 }; // 1 hour
			mockRepository.getAuthData.mockResolvedValue(futureAuthData);

			const result = await service.refreshAuthIfNeeded();

			expect(result).toBe(true);
		});

		it('should return false for token that needs refresh', async () => {
			const soonToExpireAuthData = { ...mockAuthData, expiresAt: Date.now() + 60000 }; // 1 minute
			mockRepository.getAuthData.mockResolvedValue(soonToExpireAuthData);

			const result = await service.refreshAuthIfNeeded();

			expect(result).toBe(false);
		});

		it('should return false when no auth data exists', async () => {
			mockRepository.getAuthData.mockResolvedValue(null);

			const result = await service.refreshAuthIfNeeded();

			expect(result).toBe(false);
		});
	});

	describe('storeSecureData', () => {
		it('should store valid data', async () => {
			mockRepository.storeSecureData.mockResolvedValue();

			await service.storeSecureData('test-key', { data: 'test' });

			expect(mockRepository.storeSecureData).toHaveBeenCalledWith('test-key', {
				data: 'test',
			});
		});

		it('should throw error for empty key', async () => {
			await expect(service.storeSecureData('', { data: 'test' })).rejects.toThrow(
				'Storage key cannot be empty',
			);
		});

		it('should throw error for null data', async () => {
			await expect(service.storeSecureData('test-key', null)).rejects.toThrow(
				'Cannot store null or undefined data',
			);
		});
	});

	describe('getSecureData', () => {
		it('should retrieve data successfully', async () => {
			const testData = { id: 1, name: 'Test' };
			mockRepository.getSecureData.mockResolvedValue(testData);

			const result = await service.getSecureData('test-key');

			expect(result).toEqual(testData);
			expect(mockRepository.getSecureData).toHaveBeenCalledWith('test-key');
		});

		it('should throw error for empty key', async () => {
			await expect(service.getSecureData('')).rejects.toThrow('Storage key cannot be empty');
		});

		it('should return null when no data exists', async () => {
			mockRepository.getSecureData.mockResolvedValue(null);

			const result = await service.getSecureData('test-key');

			expect(result).toBeNull();
		});
	});

	describe('removeSecureData', () => {
		it('should remove data successfully', async () => {
			mockRepository.removeSecureData.mockResolvedValue();

			await service.removeSecureData('test-key');

			expect(mockRepository.removeSecureData).toHaveBeenCalledWith('test-key');
		});

		it('should throw error for empty key', async () => {
			await expect(service.removeSecureData('')).rejects.toThrow(
				'Storage key cannot be empty',
			);
		});

		it('should throw error when removal fails', async () => {
			mockRepository.removeSecureData.mockRejectedValue(new Error('Removal failed'));

			await expect(service.removeSecureData('test-key')).rejects.toThrow(
				'Failed to remove secure data for key test-key: Error: Removal failed',
			);
		});
	});

	describe('removeSecureObject', () => {
		it('should remove object successfully', async () => {
			mockRepository.removeObject.mockResolvedValue();

			await service.removeSecureObject('test-key');

			expect(mockRepository.removeObject).toHaveBeenCalledWith('test-key');
		});

		it('should throw error for empty key', async () => {
			await expect(service.removeSecureObject('')).rejects.toThrow(
				'Storage key cannot be empty',
			);
		});

		it('should throw error for whitespace-only key', async () => {
			await expect(service.removeSecureObject('   ')).rejects.toThrow(
				'Storage key cannot be empty',
			);
		});

		it('should throw error when removal fails', async () => {
			mockRepository.removeObject.mockRejectedValue(new Error('Removal failed'));

			await expect(service.removeSecureObject('test-key')).rejects.toThrow(
				'Failed to remove secure object for key test-key: Error: Removal failed',
			);
		});
	});

	describe('hasUserData', () => {
		it('should return true when user has auth data', async () => {
			mockRepository.getAuthData.mockResolvedValue(mockAuthData);

			const result = await service.hasUserData();

			expect(result).toBe(true);
		});

		it('should return false when user has no auth data', async () => {
			mockRepository.getAuthData.mockResolvedValue(null);

			const result = await service.hasUserData();

			expect(result).toBe(false);
		});
	});
});

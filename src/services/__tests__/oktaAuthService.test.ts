/**
 * OktaAuthService Tests
 * 
 * Comprehensive unit tests for the OktaAuthService class
 */

import { OktaAuthService } from '../oktaAuthService';
import { OktaAuthRepository } from '@/repositories/oktaAuthRepository';
import { 
	OktaAuthenticationError,
	OktaAuthError,
	type OktaConfig,
	type OktaAuthResponse,
	type OktaAuthResult
} from '@/types/okta';

// Mock the token exchange utilities
jest.mock('@/utils/oktaTokenExchange', () => ({
	exchangeCodeForTokens: jest.fn(),
	refreshAccessToken: jest.fn(),
	parseIdToken: jest.fn(),
	extractUserProfile: jest.fn(),
	generatePKCEParams: jest.fn(() => ({
		codeVerifier: 'mock-verifier',
		codeChallenge: 'mock-challenge',
		codeChallengeMethod: 'S256'
	})),
	generateOAuthState: jest.fn(() => 'mock-state')
}));

// Mock the repository
const mockRepository = {
	saveAuthData: jest.fn(),
	getAuthData: jest.fn(),
	updateTokens: jest.fn(),
	getUserProfile: jest.fn(),
	saveUserProfile: jest.fn(),
	saveAuthState: jest.fn(),
	getAuthState: jest.fn(),
	savePKCEVerifier: jest.fn(),
	getPKCEVerifier: jest.fn(),
	saveOAuthState: jest.fn(),
	getOAuthState: jest.fn(),
	isAuthenticated: jest.fn(),
	needsTokenRefresh: jest.fn(),
	clearAuthData: jest.fn(),
	getAccessToken: jest.fn(),
	getRefreshToken: jest.fn(),
} as jest.Mocked<OktaAuthRepository>;

const mockConfig: OktaConfig = {
	issuer: 'https://test.okta.com/oauth2/default',
	clientId: 'test-client-id',
	redirectUri: 'testapp://callback',
	scopes: ['openid', 'profile', 'email']
};

describe('OktaAuthService', () => {
	let service: OktaAuthService;

	beforeEach(() => {
		jest.clearAllMocks();
		service = new OktaAuthService(mockRepository, mockConfig);
	});

	describe('initializeAuth', () => {
		it('should initialize authentication request successfully', async () => {
			const result = await service.initializeAuth();

			expect(result).toEqual({
				clientId: mockConfig.clientId,
				redirectUri: mockConfig.redirectUri,
				responseType: 'code',
				scopes: mockConfig.scopes,
				state: 'mock-state',
				codeChallenge: 'mock-challenge',
				codeChallengeMethod: 'S256'
			});

			expect(mockRepository.savePKCEVerifier).toHaveBeenCalledWith('mock-verifier');
			expect(mockRepository.saveOAuthState).toHaveBeenCalledWith('mock-state');
			expect(mockRepository.saveAuthState).toHaveBeenCalledWith('authenticating');
		});

		it('should handle initialization errors', async () => {
			mockRepository.savePKCEVerifier.mockRejectedValue(new Error('Storage error'));

			await expect(service.initializeAuth()).rejects.toThrow(OktaAuthenticationError);
			expect(mockRepository.saveAuthState).toHaveBeenCalledWith('error');
		});
	});

	describe('processAuthCallback', () => {
		const mockTokenResponse = {
			accessToken: 'access-token',
			tokenType: 'Bearer',
			expiresIn: 3600,
			refreshToken: 'refresh-token',
			idToken: 'id-token',
			scope: 'openid profile email'
		};

		beforeEach(() => {
			const { exchangeCodeForTokens } = require('@/utils/oktaTokenExchange');
			exchangeCodeForTokens.mockResolvedValue(mockTokenResponse);
		});

		it('should process successful callback', async () => {
			const response: OktaAuthResponse = {
				type: 'success',
				code: 'auth-code',
				state: 'mock-state'
			};

			mockRepository.getOAuthState.mockResolvedValue('mock-state');
			mockRepository.getPKCEVerifier.mockResolvedValue('mock-verifier');

			const result = await service.processAuthCallback(response);

			expect(result.success).toBe(true);
			expect(result.accessToken).toBe('access-token');
			expect(mockRepository.saveAuthData).toHaveBeenCalled();
			expect(mockRepository.saveAuthState).toHaveBeenCalledWith('authenticated');
		});

		it('should handle invalid state parameter', async () => {
			const response: OktaAuthResponse = {
				type: 'success',
				code: 'auth-code',
				state: 'invalid-state'
			};

			mockRepository.getOAuthState.mockResolvedValue('mock-state');

			await expect(service.processAuthCallback(response)).rejects.toThrow(
				'Invalid state parameter - possible CSRF attack'
			);
			expect(mockRepository.saveAuthState).toHaveBeenCalledWith('error');
		});

		it('should handle missing PKCE verifier', async () => {
			const response: OktaAuthResponse = {
				type: 'success',
				code: 'auth-code',
				state: 'mock-state'
			};

			mockRepository.getOAuthState.mockResolvedValue('mock-state');
			mockRepository.getPKCEVerifier.mockResolvedValue(null);

			await expect(service.processAuthCallback(response)).rejects.toThrow(
				'Missing PKCE verifier'
			);
		});

		it('should handle error response', async () => {
			const response: OktaAuthResponse = {
				type: 'error',
				error: 'access_denied'
			};

			await expect(service.processAuthCallback(response)).rejects.toThrow(
				OktaAuthenticationError
			);
			expect(mockRepository.saveAuthState).toHaveBeenCalledWith('error');
		});

		it('should handle cancelled response', async () => {
			const response: OktaAuthResponse = {
				type: 'cancel'
			};

			await expect(service.processAuthCallback(response)).rejects.toThrow(
				'User cancelled authentication'
			);
		});
	});

	describe('isAuthenticated', () => {
		it('should return authentication status', async () => {
			mockRepository.isAuthenticated.mockResolvedValue(true);

			const result = await service.isAuthenticated();

			expect(result).toBe(true);
			expect(mockRepository.isAuthenticated).toHaveBeenCalled();
		});

		it('should handle repository errors', async () => {
			mockRepository.isAuthenticated.mockRejectedValue(new Error('Storage error'));

			const result = await service.isAuthenticated();

			expect(result).toBe(false);
		});
	});

	describe('getUserProfile', () => {
		it('should return user profile', async () => {
			const mockProfile = {
				sub: 'user-123',
				email: '<EMAIL>',
				name: 'Test User'
			};

			mockRepository.getUserProfile.mockResolvedValue(mockProfile);

			const result = await service.getUserProfile();

			expect(result).toEqual(mockProfile);
		});

		it('should handle repository errors', async () => {
			mockRepository.getUserProfile.mockRejectedValue(new Error('Storage error'));

			const result = await service.getUserProfile();

			expect(result).toBeNull();
		});
	});

	describe('getAccessToken', () => {
		it('should return access token when valid', async () => {
			mockRepository.needsTokenRefresh.mockResolvedValue(false);
			mockRepository.getAccessToken.mockResolvedValue('access-token');

			const result = await service.getAccessToken();

			expect(result).toBe('access-token');
		});

		it('should refresh token when needed', async () => {
			mockRepository.needsTokenRefresh.mockResolvedValue(true);
			mockRepository.getRefreshToken.mockResolvedValue('refresh-token');
			
			const { refreshAccessToken } = require('@/utils/oktaTokenExchange');
			refreshAccessToken.mockResolvedValue({
				accessToken: 'new-access-token',
				tokenType: 'Bearer',
				expiresIn: 3600
			});

			mockRepository.getAccessToken.mockResolvedValue('new-access-token');

			const result = await service.getAccessToken();

			expect(result).toBe('new-access-token');
			expect(mockRepository.updateTokens).toHaveBeenCalled();
		});
	});

	describe('refreshTokenIfNeeded', () => {
		it('should refresh token successfully', async () => {
			mockRepository.getRefreshToken.mockResolvedValue('refresh-token');
			
			const { refreshAccessToken } = require('@/utils/oktaTokenExchange');
			refreshAccessToken.mockResolvedValue({
				accessToken: 'new-access-token',
				tokenType: 'Bearer',
				expiresIn: 3600
			});

			const result = await service.refreshTokenIfNeeded();

			expect(result).toBe(true);
			expect(mockRepository.updateTokens).toHaveBeenCalled();
			expect(mockRepository.saveAuthState).toHaveBeenCalledWith('authenticated');
		});

		it('should handle missing refresh token', async () => {
			mockRepository.getRefreshToken.mockResolvedValue(null);

			const result = await service.refreshTokenIfNeeded();

			expect(result).toBe(false);
			expect(mockRepository.saveAuthState).toHaveBeenCalledWith('unauthenticated');
		});

		it('should handle refresh errors', async () => {
			mockRepository.getRefreshToken.mockResolvedValue('refresh-token');
			
			const { refreshAccessToken } = require('@/utils/oktaTokenExchange');
			refreshAccessToken.mockRejectedValue(new Error('Refresh failed'));

			const result = await service.refreshTokenIfNeeded();

			expect(result).toBe(false);
			expect(mockRepository.saveAuthState).toHaveBeenCalledWith('token_expired');
		});
	});

	describe('logout', () => {
		it('should logout successfully', async () => {
			await service.logout();

			expect(mockRepository.clearAuthData).toHaveBeenCalled();
			expect(mockRepository.saveAuthState).toHaveBeenCalledWith('unauthenticated');
		});

		it('should handle logout errors', async () => {
			mockRepository.clearAuthData.mockRejectedValue(new Error('Clear failed'));

			await expect(service.logout()).rejects.toThrow(OktaAuthenticationError);
		});
	});

	describe('getAuthState', () => {
		it('should return stored auth state', async () => {
			mockRepository.getAuthState.mockResolvedValue('authenticated');

			const result = await service.getAuthState();

			expect(result).toBe('authenticated');
		});

		it('should return default state on error', async () => {
			mockRepository.getAuthState.mockRejectedValue(new Error('Storage error'));

			const result = await service.getAuthState();

			expect(result).toBe('unauthenticated');
		});
	});

	describe('updateConfig', () => {
		it('should update configuration', () => {
			const newConfig = { clientId: 'new-client-id' };

			service.updateConfig(newConfig);

			// Test that the config is updated by checking if it affects subsequent operations
			expect(() => service.updateConfig(newConfig)).not.toThrow();
		});
	});
});

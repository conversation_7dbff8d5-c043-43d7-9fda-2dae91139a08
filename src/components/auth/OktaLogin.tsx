/**
 * Okta Authentication Component
 *
 * Simple, reliable OAuth integration that works across web, iOS, and Android.
 *
 * TEAM SETUP REQUIREMENTS:
 * 1. Okta Application Configuration:
 *    - Add redirect URI: learningcoachcommunity://callback
 *    - This works for ALL team members regardless of IP address
 *
 * 2. How it works:
 *    - Development: Uses consistent custom scheme learningcoachcommunity://callback
 *    - Production: Uses makeRedirectUri for platform-specific handling
 *    - Deep linking handles the redirect back to the app
 *    - Expo Router processes the callback at /callback route
 *
 * 3. Testing:
 *    - Web: Run `npm start` and test in browser
 *    - iOS: Run `npx expo run:ios` and test on device/simulator
 *    - Android: Run `npx expo run:android` and test on device/emulator
 *    - All developers use the same redirect URI configuration
 *
 * 4. Production Ready:
 *    - makeRedirectUri handles platform-specific URI generation in production
 *    - Development uses team-friendly consistent scheme
 */

import { useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert, StyleSheet, Platform } from 'react-native';
import { useAuthRequest, useAutoDiscovery, makeRedirectUri, ResponseType } from 'expo-auth-session';
import { maybeCompleteAuthSession } from 'expo-web-browser';
import { useOktaAuth } from '@/hooks/useOktaAuth';

interface OktaLoginProps {
  onAuthSuccess?: (result: any) => void;
  onAuthError?: (error: string) => void;
}

export default function OktaLogin({ onAuthSuccess, onAuthError }: OktaLoginProps) {
  const {
    authState,
    isLoading,
    isAuthenticated,
    userProfile,
    error,
    handleAuthenticationTokens,
    logout,
    clearError
  } = useOktaAuth();

  // Okta configuration - using environment variables
  const oktaDomain = process.env.EXPO_PUBLIC_OKTA_ISSUER || 'https://integrator-5743111.okta.com/oauth2/default';
  const clientId = process.env.EXPO_PUBLIC_OKTA_CLIENT_ID || 'your-client-id-here';

  // Auto-discovery endpoint as recommended by Expo docs
  const discovery = useAutoDiscovery(oktaDomain);

  // Team-friendly redirect URI approach
  const authRedirectUri = makeRedirectUri({
    scheme: 'learningcoachcommunity',
    path: 'callback'
  });

  console.log('🔐 [Auth] Configuration:', {
    oktaDomain,
    clientId: clientId.substring(0, 8) + '...',
    platform: Platform.OS,
    redirectUri: authRedirectUri,
    discovery
  });

  const [request, response, promptAsync] = useAuthRequest(
    {
      clientId: clientId,
      scopes: ['openid', 'profile', 'email'],
      redirectUri: authRedirectUri,
      responseType: ResponseType.Code,
    },
    discovery
  );

  // Debug the response object
  console.log('🔍 [Auth] Response object:', response);
  console.log('🔍 [Auth] Response type:', response?.type);
  if (response?.type === 'success') {
    console.log('🔍 [Auth] Response params:', response.params);
  }
  maybeCompleteAuthSession();

  // Handle authentication response from useAuthRequest (for iOS and web)
  useEffect(() => {
    if (!response) return;

    console.log('🔍 [Auth] useEffect triggered with response:', {
      type: response?.type,
      hasParams: !!(response as any)?.params,
      hasAuthentication: !!(response as any)?.authentication,
      platform: Platform.OS,
      timestamp: new Date().toISOString()
    });

    const handleResponse = async () => {
      if (response.type === 'success') {
        console.log('✅ Authentication Success (useAuthRequest):', response);

        // Check if we have tokens from automatic exchange
        if ((response as any).authentication) {
          console.log('🎯 [Auth] Tokens received from automatic exchange:', (response as any).authentication);
          // Process the pre-exchanged tokens directly
          handleAuthenticationTokens((response as any).authentication);
        } else if ((response as any).params?.code) {
          console.log('🔄 [Auth] Authorization code received, exchanging for real tokens...');
          const authCode = (response as any).params.code;
          const state = (response as any).params.state;
          console.log('✅ [Auth] Authorization code:', authCode.substring(0, 20) + '...');
          console.log('🔍 [Auth] PKCE Code Verifier:', request?.codeVerifier ? 'Available' : 'Missing');

          // Import the token exchange utility directly
          const { exchangeCodeForTokens } = await import('@/utils/oktaTokenExchange');

          try {
            // Make the token exchange with PKCE verifier
            const tokenResponse = await exchangeCodeForTokens(oktaDomain, {
              grantType: 'authorization_code',
              clientId: clientId,
              code: authCode,
              redirectUri: authRedirectUri,
              codeVerifier: request?.codeVerifier, // Include PKCE verifier
            });

            console.log('🎯 [Auth] Real tokens received successfully');

            // Create authentication object for the hook
            const authData = {
              accessToken: tokenResponse.accessToken,
              tokenType: tokenResponse.tokenType,
              expiresIn: tokenResponse.expiresIn,
              refreshToken: tokenResponse.refreshToken,
              idToken: tokenResponse.idToken,
              scope: tokenResponse.scope
            };

            handleAuthenticationTokens(authData);
          } catch (tokenError) {
            console.error('❌ [Auth] Token exchange failed:', tokenError);
            onAuthError?.(`Token exchange failed: ${tokenError}`);
          }
        } else {
          console.error('❌ [Auth] No tokens or code in successful response');
          onAuthError?.('Authentication succeeded but no tokens received');
        }
      } else if (response.type === 'error') {
        console.error('❌ Authentication Error (useAuthRequest):', response.error);
        const errorMessage = response.error?.message || 'Authentication failed';
        onAuthError?.(errorMessage);
      } else if (response.type === 'cancel') {
        console.log('🚫 Authentication Cancelled (useAuthRequest)');
        onAuthError?.('Authentication cancelled');
      }
    };

    handleResponse();
  }, [response, handleAuthenticationTokens, onAuthSuccess, onAuthError, clientId, authRedirectUri, discovery]);

  // Handle authentication state changes
  useEffect(() => {
    if (error) {
      Alert.alert(
        'Authentication Error',
        error,
        [
          { text: 'OK', onPress: clearError }
        ]
      );
      onAuthError?.(error);
    }
  }, [error, onAuthError, clearError]);

  useEffect(() => {
    if (isAuthenticated && userProfile) {
      Alert.alert(
        'Authentication Success!',
        `Welcome, ${userProfile.name || userProfile.email || 'User'}!`
      );
      onAuthSuccess?.({
        success: true,
        userProfile,
        authState
      });
    }
  }, [isAuthenticated, userProfile, authState, onAuthSuccess]);

  const handleLogin = async () => {
    if (!request) {
      Alert.alert('Error', 'Authentication request not ready. Please wait and try again.');
      return;
    }

    try {
      console.log('🔐 Starting authentication with URI:', authRedirectUri);
      console.log('🔐 Full auth request config:', {
        clientId: clientId.substring(0, 8) + '...',
        scopes: ['openid', 'profile', 'email'],
        redirectUri: authRedirectUri,
        platform: Platform.OS,
        discoveryEndpoint: oktaDomain
      });

      // Start the OAuth flow with expo-auth-session
      // Note: We'll let the callback handler process the result through our service
      const result = await promptAsync();
      console.log('🔐 promptAsync result:', result);
    } catch (error) {
      console.error('🔐 Login error:', error);
      Alert.alert('Login Error', 'Failed to start authentication process');
    }
  };

  const getStatusColor = () => {
    if (isAuthenticated) return '#10B981'; // green
    if (error) return '#EF4444'; // red
    if (authState === 'authenticating' || authState === 'refreshing_token') return '#F59E0B'; // yellow
    return '#6B7280'; // gray
  };

  const getStatusText = () => {
    if (isLoading) return 'Processing...';
    if (isAuthenticated) return 'Authenticated';
    if (error) return `Error: ${error}`;
    if (authState === 'authenticating') return 'Authenticating...';
    if (authState === 'refreshing_token') return 'Refreshing token...';
    return 'Ready to authenticate';
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Okta Authentication</Text>
      <Text style={styles.subtitle}>Platform-Aware OAuth Integration</Text>

      <View style={styles.configSection}>
        <Text style={styles.configTitle}>Configuration:</Text>
        <Text style={styles.configText}>Domain: {oktaDomain}</Text>
        <Text style={styles.configText}>Client ID: {clientId.substring(0, 8)}...</Text>
        <Text style={styles.configText}>
          Platform: {require('react-native').Platform.OS}
        </Text>
        <Text style={styles.configText}>
          Redirect URI: {authRedirectUri}
        </Text>
      </View>

      <View style={styles.statusSection}>
        <View style={[styles.statusIndicator, { backgroundColor: getStatusColor() }]} />
        <Text style={[styles.statusText, { color: getStatusColor() }]}>
          {getStatusText()}
        </Text>
      </View>

      <TouchableOpacity
        style={[styles.loginButton, isLoading && styles.loginButtonDisabled]}
        onPress={handleLogin}
        disabled={!request || isLoading}
      >
        <Text style={styles.loginButtonText}>
          {isLoading ? 'Authenticating...' : 'Login with Okta'}
        </Text>
      </TouchableOpacity>

      {isAuthenticated && (
        <TouchableOpacity
          style={[styles.loginButton, { backgroundColor: '#EF4444', marginTop: 8 }]}
          onPress={logout}
        >
          <Text style={styles.loginButtonText}>
            Logout / Clear State
          </Text>
        </TouchableOpacity>
      )}

      {isAuthenticated && userProfile && (
        <View style={styles.successSection}>
          <Text style={styles.successTitle}>✅ Success!</Text>
          <Text style={styles.successText}>
            Authentication completed successfully! Tokens have been securely stored.
          </Text>

          <View style={styles.responseSection}>
            <Text style={styles.responseTitle}>👤 User Profile:</Text>
            <Text style={styles.responseText}>
              Name: {userProfile.name || 'Not provided'}
            </Text>
            <Text style={styles.responseText}>
              Email: {userProfile.email || 'Not provided'}
            </Text>
            <Text style={styles.responseText}>
              Username: {userProfile.preferredUsername || 'Not provided'}
            </Text>
            <Text style={styles.responseText}>
              User ID: {userProfile.sub?.substring(0, 20)}...
            </Text>
            <Text style={styles.responseText}>
              Platform: {Platform.OS}
            </Text>
            <Text style={styles.responseText}>
              Auth State: {authState}
            </Text>
          </View>

          <TouchableOpacity
            style={[styles.loginButton, { backgroundColor: '#EF4444' }]}
            onPress={logout}
            disabled={isLoading}
          >
            <Text style={styles.loginButtonText}>
              {isLoading ? 'Logging out...' : 'Logout'}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#F9FAFB',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#111827',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    color: '#6B7280',
  },
  configSection: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  configTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#374151',
  },
  configText: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  statusSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    paddingHorizontal: 4,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '500',
  },
  loginButton: {
    backgroundColor: '#3B82F6',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 24,
  },
  loginButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  successSection: {
    backgroundColor: '#ECFDF5',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#10B981',
  },
  successTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#065F46',
    marginBottom: 8,
  },
  successText: {
    fontSize: 14,
    color: '#047857',
    lineHeight: 20,
    marginBottom: 16,
  },
  responseSection: {
    backgroundColor: '#F3F4F6',
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  responseTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  responseText: {
    fontSize: 12,
    color: '#6B7280',
    fontFamily: 'monospace',
    marginBottom: 4,
  },
});

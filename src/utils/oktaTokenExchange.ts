/**
 * Okta Token Exchange Utilities
 * 
 * Handles OAuth 2.0 token exchange operations including:
 * - Authorization code to token exchange
 * - Token refresh
 * - PKCE (Proof Key for Code Exchange) operations
 * - ID token parsing
 */

import { Platform } from 'react-native';
import {
	OktaAuthenticationError,
	OktaAuthError,
	type OktaTokenRequest,
	type OktaTokenResponse,
	type OktaIdTokenClaims,
	type OktaUserProfile,
	type PKCEParams
} from '@/types/okta';

/**
 * Generate PKCE parameters for secure OAuth flow
 */
export function generatePKCEParams(): PKCEParams {
	// Generate a random code verifier (43-128 characters)
	const codeVerifier = generateRandomString(128);
	
	// Create code challenge using SHA256
	const codeChallenge = base64URLEncode(sha256(codeVerifier));
	
	return {
		codeVerifier,
		codeChallenge,
		codeChallengeMethod: 'S256'
	};
}

/**
 * Generate a cryptographically secure random string
 */
function generateRandomString(length: number): string {
	const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
	let result = '';
	
	// Use crypto.getRandomValues if available (web), otherwise use Math.random
	if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
		const array = new Uint8Array(length);
		crypto.getRandomValues(array);
		for (let i = 0; i < length; i++) {
			result += charset[array[i] % charset.length];
		}
	} else {
		// Fallback for React Native
		for (let i = 0; i < length; i++) {
			result += charset[Math.floor(Math.random() * charset.length)];
		}
	}
	
	return result;
}

/**
 * SHA256 hash function (simplified implementation)
 * Note: In production, consider using a proper crypto library
 */
function sha256(plain: string): ArrayBuffer {
	// This is a simplified implementation
	// In production, use expo-crypto or similar library
	const encoder = new TextEncoder();
	const data = encoder.encode(plain);
	
	// For now, return a mock hash - replace with actual SHA256
	// TODO: Implement proper SHA256 or use expo-crypto
	return data.buffer;
}

/**
 * Base64 URL encode
 */
function base64URLEncode(buffer: ArrayBuffer): string {
	const bytes = new Uint8Array(buffer);
	let binary = '';
	for (let i = 0; i < bytes.byteLength; i++) {
		binary += String.fromCharCode(bytes[i]);
	}
	
	return btoa(binary)
		.replace(/\+/g, '-')
		.replace(/\//g, '_')
		.replace(/=/g, '');
}

/**
 * Exchange authorization code for tokens
 */
export async function exchangeCodeForTokens(
	issuer: string,
	tokenRequest: OktaTokenRequest
): Promise<OktaTokenResponse> {
	const tokenEndpoint = `${issuer}/v1/token`;
	
	// Prepare form data
	const formData = new URLSearchParams();
	formData.append('grant_type', tokenRequest.grantType);
	formData.append('client_id', tokenRequest.clientId);
	
	if (tokenRequest.grantType === 'authorization_code') {
		if (!tokenRequest.code || !tokenRequest.redirectUri) {
			throw new OktaAuthenticationError(
				OktaAuthError.INVALID_REQUEST,
				'Authorization code and redirect URI are required'
			);
		}
		formData.append('code', tokenRequest.code);
		formData.append('redirect_uri', tokenRequest.redirectUri);
		
		if (tokenRequest.codeVerifier) {
			formData.append('code_verifier', tokenRequest.codeVerifier);
		}
	} else if (tokenRequest.grantType === 'refresh_token') {
		if (!tokenRequest.refreshToken) {
			throw new OktaAuthenticationError(
				OktaAuthError.INVALID_REQUEST,
				'Refresh token is required'
			);
		}
		formData.append('refresh_token', tokenRequest.refreshToken);
	}

	try {
		console.log('🔄 Exchanging code for tokens at:', tokenEndpoint);
		console.log('🔄 Request type:', tokenRequest.grantType);
		
		const response = await fetch(tokenEndpoint, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded',
				'Accept': 'application/json',
			},
			body: formData.toString(),
		});

		const responseData = await response.json();
		
		if (!response.ok) {
			console.error('❌ Token exchange failed:', responseData);
			throw new OktaAuthenticationError(
				mapOktaErrorToType(responseData.error),
				responseData.error_description || 'Token exchange failed',
				new Error(`HTTP ${response.status}: ${responseData.error}`)
			);
		}

		console.log('✅ Token exchange successful');
		
		return {
			accessToken: responseData.access_token,
			tokenType: responseData.token_type,
			expiresIn: responseData.expires_in,
			refreshToken: responseData.refresh_token,
			idToken: responseData.id_token,
			scope: responseData.scope,
		};
	} catch (error) {
		if (error instanceof OktaAuthenticationError) {
			throw error;
		}
		
		console.error('❌ Network error during token exchange:', error);
		throw new OktaAuthenticationError(
			OktaAuthError.NETWORK_ERROR,
			'Failed to exchange code for tokens',
			error as Error
		);
	}
}

/**
 * Refresh access token using refresh token
 */
export async function refreshAccessToken(
	issuer: string,
	clientId: string,
	refreshToken: string
): Promise<OktaTokenResponse> {
	return exchangeCodeForTokens(issuer, {
		grantType: 'refresh_token',
		clientId,
		refreshToken,
	});
}

/**
 * Parse and validate ID token (JWT)
 */
export function parseIdToken(idToken: string): OktaIdTokenClaims {
	try {
		// Split JWT into parts
		const parts = idToken.split('.');
		if (parts.length !== 3) {
			throw new Error('Invalid JWT format');
		}

		// Decode payload (base64url)
		const payload = parts[1];
		const decodedPayload = base64URLDecode(payload);
		const claims = JSON.parse(decodedPayload);

		// Validate required claims
		if (!claims.sub || !claims.iss || !claims.aud || !claims.exp || !claims.iat) {
			throw new Error('Missing required JWT claims');
		}

		// Check if token is expired
		const now = Math.floor(Date.now() / 1000);
		if (claims.exp < now) {
			throw new Error('ID token is expired');
		}

		return claims as OktaIdTokenClaims;
	} catch (error) {
		console.error('❌ Failed to parse ID token:', error);
		throw new OktaAuthenticationError(
			OktaAuthError.INVALID_GRANT,
			'Failed to parse ID token',
			error as Error
		);
	}
}

/**
 * Extract user profile from ID token claims
 */
export function extractUserProfile(claims: OktaIdTokenClaims): OktaUserProfile {
	return {
		sub: claims.sub,
		email: claims.email,
		emailVerified: claims.emailVerified,
		name: claims.name,
		preferredUsername: claims.preferredUsername,
		givenName: claims.givenName,
		familyName: claims.familyName,
		locale: claims.locale,
		zoneinfo: claims.zoneinfo,
		groups: claims.groups,
	};
}

/**
 * Base64 URL decode
 */
function base64URLDecode(str: string): string {
	// Add padding if needed
	let padded = str;
	while (padded.length % 4) {
		padded += '=';
	}
	
	// Replace URL-safe characters
	const base64 = padded.replace(/-/g, '+').replace(/_/g, '/');
	
	try {
		return atob(base64);
	} catch (error) {
		throw new Error('Invalid base64url encoding');
	}
}

/**
 * Map Okta error codes to our error types
 */
function mapOktaErrorToType(errorCode: string): OktaAuthError {
	switch (errorCode) {
		case 'invalid_request':
			return OktaAuthError.INVALID_REQUEST;
		case 'invalid_client':
			return OktaAuthError.INVALID_CLIENT;
		case 'invalid_grant':
			return OktaAuthError.INVALID_GRANT;
		case 'unauthorized_client':
			return OktaAuthError.UNAUTHORIZED_CLIENT;
		case 'unsupported_grant_type':
			return OktaAuthError.UNSUPPORTED_GRANT_TYPE;
		case 'invalid_scope':
			return OktaAuthError.INVALID_SCOPE;
		default:
			return OktaAuthError.UNKNOWN_ERROR;
	}
}

/**
 * Generate OAuth state parameter
 */
export function generateOAuthState(): string {
	return generateRandomString(32);
}
